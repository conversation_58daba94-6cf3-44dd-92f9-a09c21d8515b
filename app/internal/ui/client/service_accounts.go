package client

import (
	"time"

	"github.com/google/uuid"
)

type createServiceAccountRequest struct {
	ClientOrganization string `json:"client_organization" example:"example org"`
	ClientContactEmail string `json:"client_contact_email" example:"<EMAIL>"`
}
type createServiceAccountResponse struct {
	ClientID  string    `json:"client_id" example:"sa_example-org_k7j2m9x1"`
	AccountID uuid.UUID `json:"account_id" example:"550e8400-e29b-41d4-a716-************"`
	SetupURL  string    `json:"setup_url" example:"https://api.example.com/api/auth/service-accounts/setup/550e8400-e29b-41d4-a716-************"`
	ExpiresAt time.Time `json:"expires_at" example:"2024-12-25T10:30:00Z"`
	ExpiresIn int       `json:"expires_in" example:"172800"`
}

func (c *Client) CreateServiceAccount(accessToken string, req createServiceAccountRequest) (createServiceAccountResponse, error) {

	createServiceAccountResponse := createServiceAccountResponse{}

	return createServiceAccountResponse, nil

}
